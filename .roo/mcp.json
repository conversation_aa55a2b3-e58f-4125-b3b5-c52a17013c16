{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": ["resolve-library-id"], "transportType": "stdio"}, "github.com/exa-labs/exa-mcp-server": {"command": "npx", "args": ["-y", "exa-mcp-server", "--tools=web_search_exa,research_paper_search,company_research,crawling,competitor_finder,linkedin_search,wikipedia_search_exa,github_search"], "env": {"EXA_API_KEY": "7c9f95f7-e3c1-46f7-9e0b-3b90e9fede9f"}, "disabled": false, "autoApprove": ["web_search_exa"], "alwaysAllow": ["web_search_exa"]}}}